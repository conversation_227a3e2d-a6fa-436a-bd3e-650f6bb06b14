import { apiService } from './api';
import { transformCandidate } from '../utils/transformers';
import { Candidate, ApplicationStatus, FilterOptions } from '../types';
import { API_CONFIG } from '../config/constants';

export class CandidateService {
  async getAllCandidates(): Promise<Candidate[]> {
    const apiCandidates = await apiService.getCandidates();
    
    if (apiCandidates.length === 0) {
      return [];
    }
    
    // Get notes and timeline events for each candidate
    const candidatesWithDetails = await Promise.all(
      apiCandidates.map(async (apiCandidate) => {
        try {
          const [notes, timelineEvents] = await Promise.all([
            apiService.getNotes(apiCandidate.id),
            apiService.getTimelineEvents(apiCandidate.id)
          ]);
          
          return transformCandidate(apiCandidate, notes, timelineEvents);
        } catch (error) {
          console.warn(`Failed to fetch details for candidate ${apiCandidate.id}:`, error);
          // Return candidate with empty notes and timeline if details fail
          return transformCandidate(apiCandidate, [], []);
        }
      })
    );
    
    return candidatesWithDetails;
  }

  async getCandidateById(id: string): Promise<Candidate | null> {
    try {
      const [apiCandidate, notes, timelineEvents] = await Promise.all([
        apiService.getCandidate(id),
        apiService.getNotes(id),
        apiService.getTimelineEvents(id)
      ]);
      
      return transformCandidate(apiCandidate, notes, timelineEvents);
    } catch (error) {
      console.error('Get candidate error:', error);
      return null;
    }
  }

  async updateCandidate(id: string, updates: Partial<Candidate>): Promise<Candidate> {
    const apiUpdates = this.transformCandidateToApi(updates);
    const updatedApiCandidate = await apiService.updateCandidate(id, apiUpdates);
    
    // Get updated notes and timeline
    const [notes, timelineEvents] = await Promise.all([
      apiService.getNotes(id),
      apiService.getTimelineEvents(id)
    ]);
    
    return transformCandidate(updatedApiCandidate, notes, timelineEvents);
  }

  async addNote(candidateId: string, content: string): Promise<void> {
    await apiService.createNote(candidateId, content, API_CONFIG.TEMP_ADMIN_ID);
  }

  async updateStatus(id: string, status: ApplicationStatus): Promise<void> {
    await apiService.updateCandidate(id, { status });
    await apiService.createTimelineEvent(
      id, 
      API_CONFIG.TEMP_ADMIN_ID, 
      'Status Updated', 
      `Status changed to ${status}`, 
      'status_change'
    );
  }

  async toggleFlag(id: string, currentFlag: boolean): Promise<void> {
    await apiService.updateCandidate(id, { flagged: !currentFlag });
  }

  private transformCandidateToApi(candidate: Partial<Candidate>) {
    const apiCandidate: any = {};
    
    if (candidate.firstName) apiCandidate.first_name = candidate.firstName;
    if (candidate.lastName) apiCandidate.last_name = candidate.lastName;
    if (candidate.email) apiCandidate.email = candidate.email;
    if (candidate.phone) apiCandidate.phone = candidate.phone;
    if (candidate.position) apiCandidate.position = candidate.position;
    if (candidate.storeLocation) apiCandidate.store_location = candidate.storeLocation;
    if (candidate.availability) apiCandidate.availability = candidate.availability;
    if (candidate.resume) apiCandidate.resume_url = candidate.resume;
    if (candidate.status) apiCandidate.status = candidate.status;
    if (candidate.flagged !== undefined) apiCandidate.flagged = candidate.flagged;
    
    return apiCandidate;
  }
}

export const candidateService = new CandidateService();