import { ApiCandidate, ApiNote, ApiTimelineEvent, ApiAdmin } from '../types/api';
import { Candidate, Note, TimelineEvent, Admin } from '../types';

export const transformCandidate = (apiCandidate: ApiCandidate, notes: ApiNote[] = [], timelineEvents: ApiTimelineEvent[] = []): Candidate => {
  return {
    id: apiCandidate.id,
    firstName: apiCandidate.first_name,
    lastName: apiCandidate.last_name,
    email: apiCandidate.email,
    phone: apiCandidate.phone,
    position: apiCandidate.position,
    storeLocation: apiCandidate.store_location,
    availability: apiCandidate.availability,
    resume: apiCandidate.resume_url,
    status: apiCandidate.status as any,
    experience: apiCandidate.experience,
    motivation: apiCandidate.motivation,
    createdAt: apiCandidate.created_at,
    updatedAt: apiCandidate.updated_at,
    flagged: apiCandidate.flagged,
    notes: notes.map(transformNote),
    timeline: timelineEvents.map(transformTimelineEvent)
  };
};

export const transformNote = (apiNote: ApiNote): Note => {
  return {
    id: apiNote.id,
    candidateId: apiNote.candidate_id,
    text: apiNote.content,
    createdAt: apiNote.created_at,
    createdBy: 'Admin'
  };
};

export const transformTimelineEvent = (apiEvent: ApiTimelineEvent): TimelineEvent => {
  return {
    id: apiEvent.id,
    candidateId: apiEvent.candidate_id,
    title: apiEvent.title,
    description: apiEvent.description,
    date: apiEvent.created_at,
    type: apiEvent.type as any
  };
};

export const transformAdmin = (apiAdmin: ApiAdmin): Admin => {
  return {
    id: apiAdmin.id,
    username: apiAdmin.username,
    name: apiAdmin.name,
    jobTitle: apiAdmin.job_title,
    email: apiAdmin.email,
    phone: apiAdmin.phone
  };
};
export const transformAdminToApi = (admin: Partial<Admin>): Partial<ApiAdmin> => {
  const apiAdmin: Partial<ApiAdmin> = {};
  
  if (admin.id !== undefined) apiAdmin.id = admin.id;
  if (admin.username !== undefined) apiAdmin.username = admin.username;
  if (admin.name !== undefined) apiAdmin.name = admin.name;
  if (admin.jobTitle !== undefined) apiAdmin.job_title = admin.jobTitle;
  if (admin.email !== undefined) apiAdmin.email = admin.email;
  if (admin.phone !== undefined) apiAdmin.phone = admin.phone;
  
  return apiAdmin;
};