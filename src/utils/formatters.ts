export class FormatterService {
  formatDate(dateString: string): string {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    }).format(date);
  }

  formatDateTime(dateString: string): string {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  formatPhoneNumber(phone: string): string {
    // Basic phone formatting - can be enhanced based on requirements
    const cleaned = phone.replace(/\D/g, '');
    if (cleaned.length === 10) {
      return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6)}`;
    }
    return phone;
  }

  truncateText(text: string, maxLength: number): string {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
  }

  getStatusColor(status: string): string {
    switch (status) {
      case 'Hired':
        return 'bg-success bg-opacity-10 text-success';
      case 'Declined':
        return 'bg-error bg-opacity-10 text-error';
      case 'On Hold':
        return 'bg-warning bg-opacity-10 text-warning';
      case 'Archived':
        return 'bg-neutral-200 text-neutral-700';
      default:
        return 'bg-gold bg-opacity-10 text-gold-dark';
    }
  }
}

export const formatterService = new FormatterService();