import { supabase } from '../lib/supabase';
import { ApiCandidate, ApiNote, ApiTimelineEvent, ApiAdmin } from '../types/api';
import { API_CONFIG } from '../config/constants';

class ApiService {
  private handleError(error: any, operation: string) {
    console.error(`${operation} failed:`, error);
    throw new Error(error.message || `${operation} failed`);
  }

  // Auth methods
  async signIn(email: string, password: string) {
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password
      });
      
      if (error) {
        // Provide more specific error messages
        if (error.message.includes('Invalid login credentials')) {
          throw new Error('Invalid email or password. Please check your credentials.');
        } else if (error.message.includes('Email not confirmed')) {
          throw new Error('Please confirm your email address before signing in.');
        } else if (error.message.includes('Too many requests')) {
          throw new Error('Too many login attempts. Please wait a moment and try again.');
        }
        throw error;
      }
      return { data, error: null };
    } catch (error) {
      this.handleError(error, 'Sign in');
    }
  }

  async signOut() {
    try {
      const { error } = await supabase.auth.signOut();
      if (error) throw error;
      return { error: null };
    } catch (error) {
      this.handleError(error, 'Sign out');
    }
  }

  async getSession() {
    try {
      const { data, error } = await supabase.auth.getSession();
      if (error) throw error;
      return { data, error: null };
    } catch (error) {
      this.handleError(error, 'Get session');
    }
  }

  // Admin methods
  async getAdmin(id: string): Promise<ApiAdmin | null> {
    try {
      const { data, error } = await supabase
        .from('admins')
        .select('*')
        .eq('id', id)
        .single();
      
      if (error) {
        // If no rows found, return null instead of throwing
        if (error.code === 'PGRST116' || error.message.includes('no rows returned')) {
          return null;
        }
        throw error;
      }
      return data;
    } catch (error) {
      // Handle the case where no admin is found
      if (error instanceof Error && (error.message.includes('no rows returned') || error.message.includes('PGRST116'))) {
        return null;
      }
      this.handleError(error, 'Get admin');
    }
  }

  async createAdmin(admin: Omit<ApiAdmin, 'created_at' | 'updated_at'>): Promise<ApiAdmin> {
    try {
      const { data, error } = await supabase
        .from('admins')
        .insert([admin])
        .select()
        .single();
      
      if (error) throw error;
      return data;
    } catch (error) {
      this.handleError(error, 'Create admin');
    }
  }

  async updateAdmin(id: string, updates: Partial<ApiAdmin>): Promise<ApiAdmin> {
    try {
      const { data, error } = await supabase
        .from('admins')
        .update({ ...updates, updated_at: new Date().toISOString() })
        .eq('id', id)
        .select()
        .single();
      
      if (error) throw error;
      return data;
    } catch (error) {
      this.handleError(error, 'Update admin');
    }
  }

  // Candidate methods
  async getCandidates(): Promise<ApiCandidate[]> {
    try {
      console.log('🔍 Fetching candidates from Supabase...');
      
      // Check if user is authenticated
      const { data: { session } } = await supabase.auth.getSession();
      
      if (!session) {
        console.log('⚠️ No authenticated session found');
        // Return empty array instead of throwing error for unauthenticated users
        return [];
      }
      
      const { data, error } = await supabase
        .from('candidates')
        .select('*')
        .order('created_at', { ascending: false });
      
      if (error) {
        console.error('❌ Supabase error fetching candidates:', error);
        // Don't throw error for permission issues, return empty array
        if (error.code === '42501' || error.message.includes('row-level security')) {
          console.log('⚠️ RLS policy prevented access. User may need to authenticate.');
          return [];
        }
        throw error;
      }
      
      console.log('✅ Candidates fetched successfully:', data?.length || 0, 'records');
      console.log('📊 Sample candidate data:', data?.[0]);
      
      return data || [];
    } catch (error) {
      console.error('💥 Failed to fetch candidates from database:', error);
      // Return empty array instead of throwing for RLS errors
      if (error instanceof Error && (error.message.includes('row-level security') || error.message.includes('42501'))) {
        console.log('⚠️ Returning empty array due to RLS policy');
        return [];
      }
      throw error;
    }
  }

  async getCandidate(id: string): Promise<ApiCandidate> {
    try {
      const { data, error } = await supabase
        .from('candidates')
        .select('*')
        .eq('id', id)
        .single();
      
      if (error) throw error;
      return data;
    } catch (error) {
      this.handleError(error, 'Get candidate');
    }
  }

  async createCandidate(candidate: Omit<ApiCandidate, 'id' | 'created_at' | 'updated_at'>): Promise<ApiCandidate> {
    try {
      const { data, error } = await supabase
        .from('candidates')
        .insert([candidate])
        .select()
        .single();
      
      if (error) throw error;
      return data;
    } catch (error) {
      this.handleError(error, 'Create candidate');
    }
  }

  async updateCandidate(id: string, updates: Partial<ApiCandidate>): Promise<ApiCandidate> {
    try {
      const { data, error } = await supabase
        .from('candidates')
        .update({ ...updates, updated_at: new Date().toISOString() })
        .eq('id', id)
        .select()
        .single();
      
      if (error) throw error;
      return data;
    } catch (error) {
      this.handleError(error, 'Update candidate');
    }
  }

  // Notes methods
  async getNotes(candidateId: string): Promise<ApiNote[]> {
    try {
      const { data, error } = await supabase
        .from('notes')
        .select('*')
        .eq('candidate_id', candidateId)
        .order('created_at', { ascending: false });
      
      if (error) throw error;
      return data || [];
    } catch (error) {
      console.warn(`Failed to fetch notes for candidate ${candidateId}:`, error);
      return [];
    }
  }

  async createNote(candidateId: string, content: string, adminId: string = API_CONFIG.TEMP_ADMIN_ID): Promise<ApiNote> {
    try {
      const { data, error } = await supabase
        .from('notes')
        .insert({
          candidate_id: candidateId,
          admin_id: adminId,
          content
        })
        .select()
        .single();
      
      if (error) throw error;
      
      // Create timeline event
      await this.createTimelineEvent(candidateId, adminId, 'Note Added', 
        content.length > 50 ? content.substring(0, 50) + '...' : content, 'note_added');
      
      return data;
    } catch (error) {
      this.handleError(error, 'Create note');
    }
  }

  async deleteNote(id: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('notes')
        .delete()
        .eq('id', id);
      
      if (error) throw error;
    } catch (error) {
      this.handleError(error, 'Delete note');
    }
  }

  // Timeline events methods
  async getTimelineEvents(candidateId: string): Promise<ApiTimelineEvent[]> {
    try {
      const { data, error } = await supabase
        .from('timeline_events')
        .select('*')
        .eq('candidate_id', candidateId)
        .order('created_at', { ascending: false });
      
      if (error) throw error;
      return data || [];
    } catch (error) {
      console.warn(`Failed to fetch timeline events for candidate ${candidateId}:`, error);
      return [];
    }
  }

  async createTimelineEvent(
    candidateId: string, 
    adminId: string = API_CONFIG.TEMP_ADMIN_ID, 
    title: string, 
    description: string, 
    type: string
  ): Promise<ApiTimelineEvent> {
    try {
      const { data, error } = await supabase
        .from('timeline_events')
        .insert({
          candidate_id: candidateId,
          admin_id: adminId,
          title,
          description,
          type
        })
        .select()
        .single();
      
      if (error) throw error;
      return data;
    } catch (error) {
      this.handleError(error, 'Create timeline event');
    }
  }
}

export const apiService = new ApiService();