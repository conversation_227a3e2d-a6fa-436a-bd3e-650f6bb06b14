import { Link } from 'react-router-dom';
import { ThumbsUp, ThumbsDown, Calendar, Edit3 } from 'lucide-react';
import { motion } from 'framer-motion';
import { useState } from 'react';
import { Candidate } from '../../types';
import { formatterService } from '../../utils/formatters';
import CandidateEditForm from './CandidateEditForm';

interface CandidateCardProps {
  candidate: Candidate;
  onUpdate: (id: string, updates: Partial<Candidate>) => Promise<Candidate | null>;
  onToggleFlag: (id: string) => Promise<any>;
}

const CandidateCard = ({ candidate, onUpdate, onToggleFlag }: CandidateCardProps) => {
  const [isEditing, setIsEditing] = useState(false);
  
  const handleToggleFlag = async (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    await onToggleFlag(candidate.id);
  };
  
  const handleEdit = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsEditing(true);
  };

  const handleSave = async (updates: Partial<Candidate>) => {
    const result = await onUpdate(candidate.id, updates);
    if (result) {
      setIsEditing(false);
    }
    return result;
  };

  const handleCancel = () => {
    setIsEditing(false);
  };
  
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      whileHover={{ y: -2 }}
      className={`bg-white rounded-lg shadow-sm overflow-hidden transition-all duration-200 hover:shadow-md ${
        isEditing ? 'ring-2 ring-gold' : ''
      }`}
    >
      <div className="p-6">
        <div className="flex justify-between items-start mb-4">
          <div className="flex-1">
            {isEditing ? (
              <CandidateEditForm
                candidate={candidate}
                onSave={handleSave}
                onCancel={handleCancel}
              />
            ) : (
              <CandidateDisplay candidate={candidate} />
            )}
          </div>
          
          {!isEditing && (
            <div className="flex items-center space-x-2 ml-4">
              <button
                onClick={handleEdit}
                className="text-neutral-400 hover:text-gold-dark transition-colors duration-200"
                aria-label="Edit candidate"
              >
                <Edit3 size={16} />
              </button>
              <button
                onClick={handleToggleFlag}
                className={`text-neutral-400 hover:text-gold-dark transition-colors duration-200 ${
                  candidate.flagged ? 'text-gold-dark' : ''
                }`}
                aria-label={candidate.flagged ? "Unlike candidate" : "Like candidate"}
              >
                {candidate.flagged ? <ThumbsUp size={16} /> : <ThumbsDown size={16} />}
              </button>
            </div>
          )}
        </div>
        
        {!isEditing && (
          <>
            <div className="mt-4 flex items-center justify-between">
              <div>
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                  formatterService.getStatusColor(candidate.status)
                }`}>
                  {candidate.status}
                </span>
              </div>
              <div className="flex items-center text-neutral-500">
                <Calendar size={14} className="mr-1" />
                <span className="text-xs">{formatterService.formatDate(candidate.createdAt)}</span>
              </div>
            </div>
            
            <div className="mt-4 pt-4 border-t border-neutral-100">
              <Link
                to={`/candidates/${candidate.id}`}
                className="w-full btn btn-primary text-center block"
              >
                View Details
              </Link>
            </div>
          </>
        )}
      </div>
    </motion.div>
  );
};

const CandidateDisplay = ({ candidate }: { candidate: Candidate }) => (
  <>
    <h3 className="text-lg font-medium text-black">
      {candidate.firstName} {candidate.lastName}
    </h3>
    <p className="text-sm text-neutral-600 mt-1">{candidate.position}</p>
    <p className="text-xs text-neutral-500 mt-1">{candidate.email}</p>
    <p className="text-xs text-neutral-500">{formatterService.formatPhoneNumber(candidate.phone)}</p>
    <p className="text-xs text-neutral-500">{candidate.storeLocation}</p>
    <div className="flex flex-wrap gap-1 mt-2">
      {candidate.availability.map((avail) => (
        <span
          key={avail}
          className="px-2 py-1 text-xs bg-neutral-100 text-neutral-600 rounded-full"
        >
          {avail}
        </span>
      ))}
    </div>
  </>
);

export default CandidateCard;