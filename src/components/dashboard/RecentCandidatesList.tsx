import { Link } from 'react-router-dom';
import { ChevronRight, ThumbsUp, ThumbsDown } from 'lucide-react';
import { Candidate } from '../../types';
import { formatterService } from '../../utils/formatters';

interface RecentCandidatesListProps {
  candidates: Candidate[];
  onToggleFlag?: (id: string) => void;
}

const RecentCandidatesList = ({ candidates, onToggleFlag }: RecentCandidatesListProps) => {
  console.log('📋 RecentCandidatesList render - candidates:', candidates.length);
  
  // Get only the 5 most recent candidates
  const recentCandidates = [...candidates]
    .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
    .slice(0, 5);
  
  const handleToggleFlag = (e: React.MouseEvent, candidateId: string) => {
    e.preventDefault();
    e.stopPropagation();
    onToggleFlag?.(candidateId);
  };
  
  return (
    <div className="bg-white shadow-sm rounded-lg overflow-hidden">
      <div className="px-4 py-5 sm:px-6 border-b border-neutral-200">
        <h3 className="text-lg font-medium leading-6 text-black">Recent Candidates</h3>
        <p className="mt-1 max-w-2xl text-sm text-neutral-500">
          The most recently submitted applications
        </p>
      </div>
      
      <ul className="divide-y divide-neutral-200">
        {recentCandidates.length > 0 ? (
          recentCandidates.map((candidate) => (
            <li key={candidate.id} className="relative hover:bg-neutral-50">
              <div className="px-4 py-4 sm:px-6 flex items-center justify-between">
                <div className="flex items-center min-w-0">
                  <div className="min-w-0">
                    <p className="text-sm font-medium text-black truncate">
                      {candidate.firstName} {candidate.lastName}
                    </p>
                    <p className="text-sm text-neutral-500 truncate">
                      {candidate.position} - {candidate.storeLocation}
                    </p>
                    <div className="mt-1">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        formatterService.getStatusColor(candidate.status)
                      }`}>
                        {candidate.status}
                      </span>
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center">
                  {onToggleFlag && (
                    <button 
                      onClick={(e) => handleToggleFlag(e, candidate.id)}
                      className={`mr-4 text-neutral-400 hover:text-gold-dark transition-colors duration-200 ${
                        candidate.flagged ? 'text-gold-dark' : ''
                      }`}
                      aria-label={candidate.flagged ? "Unlike candidate" : "Like candidate"}
                    >
                      {candidate.flagged ? <ThumbsUp size={18} /> : <ThumbsDown size={18} />}
                    </button>
                  )}
                  
                  <Link 
                    to={`/candidates/${candidate.id}`}
                    className="text-sm font-medium text-gold-dark hover:text-gold-light flex items-center"
                  >
                    View
                    <ChevronRight size={16} className="ml-1" />
                  </Link>
                </div>
              </div>
            </li>
          ))
        ) : (
          <li className="px-4 py-6 text-center text-neutral-500">
            No candidates found
          </li>
        )}
      </ul>
      
      <div className="bg-neutral-50 px-4 py-3 border-t border-neutral-200 text-right">
        <Link
          to="/candidates"
          className="text-sm font-medium text-gold-dark hover:text-gold-light"
        >
          View all candidates
        </Link>
      </div>
    </div>
  );
};

export default RecentCandidatesList;