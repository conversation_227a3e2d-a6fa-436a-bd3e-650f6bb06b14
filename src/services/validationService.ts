import { Availability } from '../types';

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

export interface ApplicationFormData {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  position: string;
  storeLocation: string;
  availability: Availability[];
  experience: string;
  motivation: string;
  resumeFile: File | null;
}

export class ValidationService {
  validateApplicationForm(formData: ApplicationFormData): ValidationResult {
    const errors: string[] = [];

    if (!formData.firstName.trim()) {
      errors.push('First name is required');
    }

    if (!formData.lastName.trim()) {
      errors.push('Last name is required');
    }

    if (!formData.email.trim()) {
      errors.push('Email is required');
    } else if (!this.isValidEmail(formData.email)) {
      errors.push('Please enter a valid email address');
    }

    if (!formData.phone.trim()) {
      errors.push('Phone number is required');
    }

    if (!formData.position) {
      errors.push('Position is required');
    }

    if (!formData.storeLocation) {
      errors.push('Store location is required');
    }

    if (formData.availability.length === 0) {
      errors.push('At least one availability option is required');
    }

    if (!formData.experience.trim()) {
      errors.push('Experience description is required');
    }

    if (!formData.motivation.trim()) {
      errors.push('Motivation is required');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  validateCandidateUpdate(updates: any): ValidationResult {
    const errors: string[] = [];

    if (updates.firstName !== undefined && !updates.firstName.trim()) {
      errors.push('First name cannot be empty');
    }

    if (updates.lastName !== undefined && !updates.lastName.trim()) {
      errors.push('Last name cannot be empty');
    }

    if (updates.email !== undefined) {
      if (!updates.email.trim()) {
        errors.push('Email cannot be empty');
      } else if (!this.isValidEmail(updates.email)) {
        errors.push('Please enter a valid email address');
      }
    }

    if (updates.availability !== undefined && updates.availability.length === 0) {
      errors.push('At least one availability option is required');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }
}

export const validationService = new ValidationService();