import { Candidate, FilterOptions, Availability, ApplicationStatus } from '../types';

export class FilterService {
  filterCandidates(candidates: Candidate[], filterOptions: FilterOptions): Candidate[] {
    return candidates.filter(candidate => {
      // Search filter
      if (filterOptions.search && !this.matchesSearch(candidate, filterOptions.search)) {
        return false;
      }
      
      // Position filter
      if (filterOptions.position && candidate.position !== filterOptions.position) {
        return false;
      }
      
      // Availability filter
      if (filterOptions.availability.length > 0 && !this.matchesAvailability(candidate, filterOptions.availability)) {
        return false;
      }
      
      // Store location filter
      if (filterOptions.storeLocation && candidate.storeLocation !== filterOptions.storeLocation) {
        return false;
      }
      
      // Status filter
      if (filterOptions.status.length > 0 && !filterOptions.status.includes(candidate.status)) {
        return false;
      }
      
      return true;
    });
  }

  private matchesSearch(candidate: Candidate, searchTerm: string): boolean {
    const searchValue = searchTerm.toLowerCase();
    const fullName = `${candidate.firstName} ${candidate.lastName}`.toLowerCase();
    
    return fullName.includes(searchValue) || 
           candidate.position.toLowerCase().includes(searchValue) ||
           candidate.email.toLowerCase().includes(searchValue);
  }

  private matchesAvailability(candidate: Candidate, filterAvailability: Availability[]): boolean {
    return candidate.availability.some(avail => filterAvailability.includes(avail));
  }

  createDefaultFilters(): FilterOptions {
    return {
      position: '',
      availability: [],
      storeLocation: '',
      status: [],
      search: '',
    };
  }
}

export const filterService = new FilterService();